steps:
  # Build backend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-f'
      - './backend/Dockerfile'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-backend:$BUILD_ID'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-backend:latest'
      - './backend'
    id: 'build-backend'

  # Push backend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/supplyline-backend:$BUILD_ID'
    id: 'push-backend'
    waitFor: ['build-backend']

  # Deploy backend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'supplylinemrosuite'
      - '--image'
      - 'gcr.io/$PROJECT_ID/supplyline-backend:$BUILD_ID'
      - '--region'
      - 'us-west1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8080'
      - '--memory'
      - '1Gi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'FLASK_ENV=production,DB_HOST=/cloudsql/gen-lang-client-0819985982:us-west1:supplyline-db,DB_USER=supplyline_user,DB_NAME=supplyline,PYTHONDONTWRITEBYTECODE=1,PYTHONUNBUFFERED=1,CORS_ORIGINS=*'
      - '--set-secrets'
      - 'SECRET_KEY=supplyline-secret-key:latest,DB_PASSWORD=supplyline-db-password:latest'
      - '--set-cloudsql-instances'
      - 'gen-lang-client-0819985982:us-west1:supplyline-db'
    id: 'deploy-backend'
    waitFor: ['push-backend']

  # Build frontend image with backend URL
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-f'
      - './frontend/Dockerfile'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:$BUILD_ID'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:latest'
      - '--build-arg'
      - 'VITE_API_URL=https://supplylinemrosuite-454313121816.us-west1.run.app'
      - './frontend'
    id: 'build-frontend'
    waitFor: ['deploy-backend']

  # Push frontend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:$BUILD_ID'
    id: 'push-frontend'
    waitFor: ['build-frontend']

  # Deploy frontend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'supplyline-frontend-production'
      - '--image'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:$BUILD_ID'
      - '--region'
      - 'us-west1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '80'
      - '--memory'
      - '512Mi'
      - '--cpu'
      - '0.5'
      - '--max-instances'
      - '5'
    id: 'deploy-frontend'
    waitFor: ['push-frontend']

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

# Build timeout
timeout: '1200s'

# Images to be pushed to Container Registry
images:
  - 'gcr.io/$PROJECT_ID/supplyline-backend:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/supplyline-backend:latest'
  - 'gcr.io/$PROJECT_ID/supplyline-frontend:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/supplyline-frontend:latest'
